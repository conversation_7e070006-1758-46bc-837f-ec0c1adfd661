<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checking network status - Gemini</title>
    <script src="renderer.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Ubuntu, Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: #f3f2f1;
            color: #323130;
        }
        .container {
            text-align: center;
            padding: 20px;
            background: #f3f2f0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        h1 {
            font-size: 2em;
            color: #000000;
        }
        p {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Checking Network Status...</h1>
    </div>
</body>
</html>
