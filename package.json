{"name": "gemini-desktop", "title": "Gemini Desktop", "version": "@@VERSION@@", "description": "Unofficial Web app for Google Gemini providing the desktop user experience you would expect on Ubuntu or any other Linux desktop.", "main": "index.js", "scripts": {"start": "electron .", "build": "electron-builder --publish never"}, "build": {"asar": false, "linux": {"target": ["dir"], "category": "Utility", "artifactName": "${name}_${version}_linux.${ext}"}, "extraFiles": ["com.github.kenvandine.${name}.desktop"]}, "repository": {"type": "git", "url": "git+https://github.com/kenvandine/gemini-desktop.git"}, "author": "<PERSON>", "license": "GPL-3.0", "bugs": {"url": "https://github.com/kenvandine/gemini-desktop/issues"}, "homepage": "https://github.com/kenvandine/gemini-desktop#readme", "devDependencies": {"electron": "^37.2.6", "electron-builder": "^25.1.8", "electron-package": "^0.1.0"}}