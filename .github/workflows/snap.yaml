name: Snap

on:
  push:
    branches: [main]
  release:
    types: [published]
  workflow_dispatch:

jobs:
  build-amd64:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        fetch-tags: true
    - uses: snapcore/action-build@v1
      id: build
    - uses: snapcore/action-publish@v1
      env:
        SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.STORE_LOGIN }}
      with:
        snap: ${{ steps.build.outputs.snap }}
        release: edge
  build-arm64:
    runs-on: ubuntu-24.04-arm
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        fetch-tags: true
    - uses: snapcore/action-build@v1
      id: build
    - uses: snapcore/action-publish@v1
      env:
        SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.STORE_LOGIN }}
      with:
        snap: ${{ steps.build.outputs.snap }}
        release: edge
